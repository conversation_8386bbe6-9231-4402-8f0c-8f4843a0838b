<!-- cursor测试:1 -->
<template>
  <div>
    <!--<Title
      :name="
        formData.sqcgfl == 'xm'
          ? '大额采购申请'
          : formData.sqcgfl == 'lx'
            ? '小额采购申请'
            : '零星维修申请'
      "
    >-->
    <Title name="基本信息">
      <div v-if="editable">
        <el-button type="primary" @click="handleSaveSq()">保存</el-button>
        <el-button type="danger" @click="handleSubmitSq()">提交</el-button>
      </div>
    </Title>

    <el-form
      ref="dataFormRef"
      :model="formData"
      :rules="sqrules"
      label-width="160px"
      :inline="true"
      :disabled="!editable"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="申请类型">
            <el-input v-model="displayText" disabled="true" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="采购项目名称" prop="sqmc">
            <el-input v-model="formData.sqmc" placeholder="申请名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请部门" prop="sqbmmc">
            <DDLDeptList v-model="formData.sqbm" :disabled="true" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="部门审核人" prop="bmfzr">
            <DDLUserList
              :key="formData.sqbm"
              v-model="formData.bmfzr"
              rcode="0206"
              :dcode="formData.sqbm"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="formData.by14 == '0'">
          <el-form-item label="业务分管校领导" prop="by13">
            <DDLUserList :key="formData.sqbm" v-model="formData.by13" rcode="0229" :dcode="''" />
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.sqcgfl === 'wx'">
          <el-form-item label="派单校区" prop="gzpdxq">
            <DDLXcode v-model="formData.gzpdxq" xcode="02159K" />
          </el-form-item>
        </el-col>

        <el-col :span="8" style="display: none">
          <el-form-item label="是否部门自行采购">
            <el-radio-group v-model="formData.by14">
              <el-radio value="1">是</el-radio>
              <el-radio value="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" :style="formData.sqcgfl == 'wx' ? 'display:none' : ''">
          <el-form-item label="采购类别" prop="cgml">
            <el-radio-group v-model="formData.cgml">
              <el-radio value="A" class="custom-radio">货物</el-radio>
              <el-radio value="B" class="custom-radio">工程</el-radio>
              <el-radio value="C" class="custom-radio">服务</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.cgml === 'wx'">
          <el-form-item label="服务类别" prop="fwlx">
            <DDLXcode v-model="formData.fwlx" xcode="024801" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="申请人办公电话" prop="sqrzj">
            <el-input v-model="formData.sqrzj" placeholder="申请人办公电话" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="项目部门" prop="sqrzj">
            <DDLDeptList v-model="formData.sqbm" :disabled="true" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="业务归口管理部门" prop="zgbmlist">
            <DDLGkDeptList v-model="formData.zgbmlist" :multiple="true" clearable />

            <!-- <el-input v-model="formData.zgbm" placeholder="主管部门" />-->
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <!--预算经费开始-->
          <Title name="经费卡和申请预算">
            <div>
              <el-button
                v-if="editable"
                type="primary"
                plain
                icon="plus"
                @click="handleOpenDialogYs(true)"
              >
                新增
              </el-button>
            </div>
          </Title>
          <el-table
            v-loading="loading"
            :data="jfpageData"
            highlight-current-row
            :border="true"
            height="200px"
          >
            <el-table-column type="index" label="序号" width="55" />
            <el-table-column prop="jfbh" label="经费卡号" width="130" />
            <el-table-column prop="jfmc" label="经费名称" :width="editable ? '300' : '450'" />
            <el-table-column prop="jhje" label="可用余额(元)" width="120" />
            <el-table-column prop="fzrname" label="经费负责人" width="155" />
            <el-table-column prop="jflbname" label="经费类别" width="150" />
            <el-table-column prop="ysje" label="申请预算(元)" width="120" />
            <el-table-column label="操作" fixed="right" width="150" v-if="editable">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  icon="Edit"
                  link
                  @click="
                    formType.view = false;
                    handleOpenDialogYs(true, scope.row);
                  "
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  link
                  @click="handleYsDelete(scope.row.jfid)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- ********************** 翻页 ********************** -->
          <pagination
            v-if="jftotal > 10"
            v-model:total="jftotal"
            v-model:page="jfqueryParams.pageNum"
            v-model:limit="jfqueryParams.pageSize"
            @pagination="jfhandlePageQuery"
          />

          <!--预算数据-->

          <!--预算经费结束-->
        </el-col>
        <el-col :span="24">
          <Title name="其他信息" />
        </el-col>
        <el-col :span="8" v-if="formData.by14 == '0'">
          <el-form-item label="采购意向" prop="yxmc">
            <div class="flex">
              <el-input v-model="formData.yxmc" :disabled="true" placeholder="请选择采购意向" />
              <el-button type="primary" @click="handleAddCgyxPorperties" v-if="editable">
                选择
              </el-button>
              <el-input v-model="formData.sqyxguid" placeholder="采购意向" style="display: none" />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预计当年支付时间" prop="yjdnzfsj">
            <el-date-picker
              v-model="formData.yjdnzfsj"
              format="YYYY-MM-DD"
              type="date"
              placeholder="请选择日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预计当年支付金额" prop="yjdnzfje">
            <el-input v-model="formData.yjdnzfje" placeholder="预计当年支付金额" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="是否进口" prop="isjk">
            <el-radio-group v-model="formData.isjk">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.isjk === '1'">
          <el-form-item label="进口论证" prop="by7">
            <div class="flex">
              <el-input
                v-model="formData.jklzmc"
                :disabled="true"
                placeholder="选取进口专家论证意见或在采购申请附件中上传"
                style="width: 200px"
              />
              <el-button type="primary" @click="handleAddJklzPorperties" v-if="editable">
                选择
              </el-button>
              <el-input
                v-model="formData.by7"
                placeholder="选取进口专家论证意见或在采购申请附件中上传"
                style="display: none"
              />
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="是否单一来源" prop="isdyly">
            <el-radio-group v-model="formData.isdyly">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.isdyly === '1'">
          <el-form-item label="单一来源论证" prop="dylzid">
            <div class="flex">
              <el-input
                v-model="formData.dylzmc"
                :disabled="true"
                placeholder="选取单一来源专家论证意见或在采购申请附件中上传"
                style="width: 160px"
              />
              <el-button type="primary" @click="handleAddDylzPorperties" v-if="editable">
                选择
              </el-button>
              <el-input
                v-model="formData.dylzid"
                placeholder="选取单一来源专家论证意见或在采购申请附件中上传"
                style="display: none"
              />
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="是否面向中小企业" prop="by2">
            <el-radio-group v-model="formData.by2">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.by2 === '0'">
          <el-form-item label="不面向中小企业的理由" prop="by9">
            <DDLYcodein v-model="formData.by9" lb="11" />
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.by14 === '0' && formData.sqcgfl != 'wx'">
          <el-form-item label="是否学科办经费" prop="by17">
            <el-radio-group v-model="formData.by17">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" style="display: none">
          <el-form-item label="是否零星维修工程" prop="by16">
            <el-input v-model="formData.by16" placeholder="是否零星维修工程" />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="is_fgw">
          <el-form-item label="是否发改委项目" prop="sffgw">
            <el-radio-group v-model="formData.sffgw">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.by14 === '0' && formData.cgml == 'A'">
          <el-form-item label="是否科研仪器设备" prop="iskysb">
            <el-radio-group v-model="formData.iskysb">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="formData.cgml == 'A'">
          <el-form-item label="是否信创七类" prop="sfxcfl">
            <el-radio-group v-model="formData.sfxcfl">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="formData.sfxcfl == '1'">
          <el-form-item label="是否信创" prop="sfxc">
            <el-radio-group v-model="formData.sfxc">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="formData.cgml == 'B'">
          <el-form-item label="必须招标的工程项目" prop="zbgcxm">
            <el-radio-group v-model="formData.zbgcxm">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <!--是否属于按照招标投标法必须进行招标的工程建设项目-->
        </el-col>
        <el-col :span="8" v-if="formData.by14 == '1'">
          <el-form-item label="本单位三重一大事项 " prop="sfszyd">
            <el-radio-group v-model="formData.sfszyd">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <!--是否属于本单位三重一大事项范围-->
        </el-col>

        <el-col :span="8" v-if="formData.by14 == '1' && formData.cgml == 'A'">
          <el-form-item label="是否政采云采购 " prop="sfzcy">
            <el-radio-group v-model="formData.sfzcy">
              <el-radio value="0">否</el-radio>
              <el-radio value="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注" prop="gzly" style="width: 1000px">
            <el-input
              v-model="formData.gzly"
              placeholder="备注"
              type="textarea"
              maxlength="200"
              show-word-limit
              :rows="3"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="formData.by14 == '1'">
          <el-form-item label="采购需求" prop="kxxfx" style="width: 1000px">
            <el-input
              v-model="formData.kxxfx"
              placeholder="采购需求"
              type="textarea"
              maxlength="2000"
              show-word-limit
              :rows="3"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <!--申请明细开始-->
      <Title name="采购申请明细">
        <div>
          <el-button
            v-if="editable"
            type="primary"
            plain
            icon="plus"
            @click="handleOpenDialogMx(true)"
          >
            新增
          </el-button>
        </div>
      </Title>
      <el-table
        v-loading="loading"
        :data="mxpageData"
        highlight-current-row
        :border="true"
        height="260px"
      >
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column prop="mxbh" label="明细编号" width="155" />
        <el-table-column
          prop="wpmc"
          :label="
            formData.cgml === 'A' ? '物品名称' : formData.cgml === 'B' ? '工程名称' : '服务名称'
          "
          :width="formData.cgml === 'A' ? '300' : '720'"
        />
        <el-table-column v-if="formData.cgml === 'A'" prop="gg" label="规格型号" width="200" />
        <el-table-column v-if="formData.cgml === 'A'" prop="sl" label="数量" width="60" />
        <el-table-column v-if="formData.cgml === 'A'" prop="dwname" label="计量单位" width="85" />
        <el-table-column v-if="formData.cgml === 'A'" prop="price" label="单价(元)" width="85" />
        <el-table-column prop="ysje" label="预算金额(元)" width="120" />
        <el-table-column label="操作" fixed="right" width="130">
          <template #default="scope">
            <el-button
              v-hasPerm="'sys:user:password:reset'"
              type="primary"
              icon="View"
              size="small"
              link
              @click="handleOpenDialogMx(false, scope.row)"
              v-if="editable == false"
            >
              查看
            </el-button>

            <el-button
              type="primary"
              size="small"
              icon="Edit"
              link
              @click="
                formType.view = false;
                handleOpenDialogMx(true, scope.row);
              "
              v-if="editable"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              icon="Delete"
              link
              @click="handleMxDelete(scope.row.mxid)"
              v-if="editable"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- ********************** 翻页 ********************** -->
      <pagination
        v-if="mxtotal > 10"
        v-model:total="mxtotal"
        v-model:page="mxqueryParams.pageNum"
        v-model:limit="mxqueryParams.pageSize"
        @pagination="mxhandlePageQuery"
      />
      <!--预算数据-->

      <!--申请明细结束-->
    </div>
    <!-- 附件上传 -->
    <div v-if="editable">
      <Title name="附件上传" />
      <FileUpload :guid="props.guid" :code="formData.fjcode" :key="formData.guid" />
    </div>
    <!-- 附件查看 -->
    <div v-else>
      <Title name="附件查看" />
      <FileView :guid="props.guid" />
    </div>
  </div>

  <!-- 选择意向弹窗 -->
  <el-drawer v-model="choosedialog.visible" :title="choosedialog.title" append-to-body size="65%">
    <CgyxChoose
      :key="keyId"
      :ysxmbh="formData.ysxmbhs"
      :handleReturnConfirm="handleReturnConfirm"
    />
  </el-drawer>

  <!-- 选择进口论证弹窗 -->
  <el-drawer
    v-model="choosedialog_jk.visible"
    :title="choosedialog_jk.title"
    append-to-body
    size="65%"
  >
    <CglzChoose
      :key="keyId"
      :xmbh="formData.ysxmbhs"
      :xmmc="formData.xmmc"
      pslx="02159208"
      :handleReturnConfirm="handleReturnConfirm_jk"
    />
  </el-drawer>

  <!-- 选择单一来源论证弹窗 -->
  <el-drawer
    v-model="choosedialog_dy.visible"
    :title="choosedialog_dy.title"
    append-to-body
    size="65%"
  >
    <CglzChoose
      :key="keyId"
      :xmbh="formData.ysxmbhs"
      :xmmc="formData.xmmc"
      pslx="02159205"
      :handleReturnConfirm="handleReturnConfirm_dy"
    />
  </el-drawer>

  <!--经费卡添加弹窗-->
  <el-dialog
    v-model="choosedialog_ys.visible"
    width="40%"
    :title="choosedialog_ys.title"
    :before-close="handleCloseYsDialog"
  >
    <SqjfEdit
      :sqid="formData.sqid"
      :RefreshFatherDrawer="YsRefreshFatherDrawer"
      ref="ysRef"
      :id="itemJfId"
      :editable="itemJfEditable"
      :key="itemJfId + '1'"
    />
    <!-----------------------------申请明细-- <template #footer>
      <el-button @click="handleCloseYsDialog">取消</el-button>
      <el-button type="primary" @click="handleAddYs">确定</el-button>
    </template>-->
  </el-dialog>

  <!--明细添加弹窗-->
  <el-dialog
    v-model="choosedialog_mx.visible"
    width="40%"
    :title="choosedialog_mx.title"
    :before-close="handleCloseMxDialog"
  >
    <MxEdit
      :sqid="formData.sqid"
      :RefreshFatherDrawer="MxRefreshFatherDrawer"
      ref="mxRef"
      :id="itemMxId"
      :editable="itemMxEditable"
      :key="itemMxGuid + '1'"
      :parentguid="formData.guid"
      :guid="itemMxGuid"
      :cglb="formData.cgml"
    />

    <!-- <template #footer>
      <el-button @click="handleCloseMxDialog">取消</el-button>
      <el-button type="primary" @click="handleAddMx">确定</el-button>
    </template>-->
  </el-dialog>
  <!--明细添加弹窗11-->
</template>

<script setup lang="ts">
defineOptions({
  name: "TransferEdit",
});
import { ElLoading } from "element-plus";
import { useUserStore } from "@/store";
import ZxcgSqAPI, { ZxcgSqPageVO, ZxcgSqForm, ZxcgSqPageQuery } from "@/api/cg/zxcg-sq";
import { getGuid } from "@/utils/guid";
import WorkLzSqbAPI, { WorkLzSqbForm } from "@/api/cg/work-lz-sqb";
import ZxcgSqjfAPI, { ZxcgSqjfForm, ZxcgSqjfPageQuery, ZxcgSqjfPageVO } from "@/api/cg/zxcg-sqjf";
import ZxcgMxAPI, { ZxcgMxForm, ZxcgMxPageVO, ZxcgMxPageQuery } from "@/api/cg/zxcg-mx";
import { formatLocalDateTime } from "@/utils/day";
import CglzChoose from "@/views/cg/worklzsqb/components/cglz-choose.vue";
import SqjfEdit from "@/views/cg/sqjf/components/sqjf-Edit.vue";
import MxEdit from "@/views/cg/mx/components/Mx-Edit.vue";
import DDLYcodein from "@/views/cg/ycodein/components/index.vue";
import { number } from "echarts";
//————————————————————————————————————————————暴露的方法,和请求参数
//组件参数
const props = defineProps({
  sqid: {
    type: Number,
  },
  guid: {
    type: String,
  },
  //保存成功后需要关闭抽屉并刷新数据
  RefreshFatherDrawer: {
    type: Function,
    required: true,
  },
  //是否可编辑
  editable: {
    type: Boolean,
  },
  sqcgfl: {
    type: String,
  },
});

//——————————————————————————————————————————————————form查询相关
const loading = ref(false);
const userStore = useUserStore();
/** 打开采购申请弹窗 */
function handleSqAdd(cgfl?: string, id?: number) {
  console.log("handleSqAdd::");
  if (cgfl && cgfl != "") formData.sqcgfl = cgfl;
  handleSqEdit(id);
}
function handleSqEdit(id?: number) {
  if (id) {
    ZxcgSqAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
      //进口论证
      if (formData.by7) {
        get_lzname(formData.by7).then((name) => {
          formData.jklzmc = name || "";
        });
      }
      //单一来源论证
      if (formData.dylzid) {
        get_lzname(formData.dylzid).then((name) => {
          formData.dylzmc = name || "";
        });
      }
      if (formData.zgbm) formData.zgbmlist = (formData.zgbm || "").split(",");
      if (formData.by14 == "0") {
        formData.sqcgfl = "xm";
      } else {
        if (formData.by16 == "1") {
          formData.sqcgfl = "wx";
        } else {
          formData.sqcgfl = "lx";
        }
      }
      if (formData.cgml == "A") {
        formData.fjcode = "02157101";
      } else if (formData.cgml == "B") {
        formData.fjcode = "02157102";
      } else if (formData.cgml == "C") {
        formData.fjcode = "02157103";
      } else {
      }
      //绑定zxcg_sqjf
      jfhandlePageQuery(); //绑定经费

      mxhandlePageQuery(); //绑定明细
      //  formData.cgmlname = findInTree(Cgmlcodes.value, formData.cgml);
    });
  } else {
    formData.sqbm = userStore.userInfo.dcode;
    formData.guid = getGuid();
    formData.cgml = "A";
    if (formData.sqcgfl == "xm") {
      formData.by14 = "0";
    } else if (formData.sqcgfl == "lx") {
      formData.by14 = "1";
    } else if (formData.sqcgfl == "wx") {
      formData.by14 = "1";
      formData.by16 = "1";
      formData.cgml = "B";
    }
    if (formData.cgml == "A") {
      formData.fjcode = "02157101";
    } else if (formData.cgml == "B") {
      formData.fjcode = "02157102";
    } else if (formData.cgml == "C") {
      formData.fjcode = "02157103";
    } else {
    }
  }

  //console.log("formData.fjcode", formData.fjcode);
}

/** 关闭采购申请弹窗 */
function handleClearForm() {
  //dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.sqid = undefined;
  //Object.assign(formData, {});
}
const displayText = computed(() => {
  switch (formData.sqcgfl) {
    case "xm":
      return "大额采购申请";
    case "lx":
      return "小额采购申请";
    default:
      return "零星维修申请";
  }
});
//—————————————————————————————————————————————意向选择弹窗
const keyId = ref(0);
// 弹窗显隐
const choosedialog = reactive({
  title: "采购意向",
  visible: false,
});
const handleAddCgyxPorperties = async () => {
  choosedialog.visible = true;
};
//暴露的方法
const handleReturnConfirm = (yxguid: string, cgxmmc: string) => {
  if (yxguid == null || yxguid == "") {
    ElMessage.warning("请选择要采购意向");
    return;
  }

  formData.yxmc = cgxmmc;
  formData.sqyxguid = yxguid;
  choosedialog.visible = false;
};
//—————————————————————————————————————————————进口论证选择弹窗
//const lz_form = reactive<WorkLzSqbForm>({});
const get_lzname = (lzid: string): Promise<string | undefined> => {
  if (!lzid) {
    return Promise.resolve(undefined);
  }

  return WorkLzSqbAPI.getFormData(Number(lzid))
    .then((data) => data.pstm)
    .catch((error) => {
      console.error("获取论证项目名称失败:", error);
      return undefined; // 错误时返回 undefined
    });
};
// 弹窗显隐
const choosedialog_jk = reactive({
  title: "进口论证",
  visible: false,
});
const handleAddJklzPorperties = async () => {
  choosedialog_jk.visible = true;
};
const handleReturnConfirm_jk = (lzid: string, pstm: string) => {
  if (lzid == null || lzid == "") {
    ElMessage.warning("请选择要进口论证");
    return;
  }

  formData.jklzmc = pstm;
  formData.by7 = lzid;
  choosedialog_jk.visible = false;
};
//—————————————————————————————————————————————单一来源论证选择弹窗
// 弹窗显隐
const choosedialog_dy = reactive({
  title: "单一来源论证",
  visible: false,
});
const handleAddDylzPorperties = async () => {
  choosedialog_dy.visible = true;
};
const handleReturnConfirm_dy = (lzid: string, pstm: string) => {
  if (lzid == null || lzid == "") {
    ElMessage.warning("请选择要进口论证");
    return;
  }

  formData.dylzmc = pstm;
  formData.dylzid = lzid;
  choosedialog_dy.visible = false;
};
//-----------------------------------------预算选择弹窗
const jfpageData = ref<ZxcgSqjfPageVO[]>([]); //经费列表
const jftotal = ref(0);
//请求参数
const jfqueryParams = reactive<ZxcgSqjfPageQuery>({
  pageNum: 1,
  pageSize: 10,
  sqid: 0,
});
//列表查询
const jfhandlePageQuery = () => {
  if (formData.sqid) jfqueryParams.sqid = formData.sqid;
  else formData.sqid = 0;
  // console.log("jfqueryParams.sqid", jfqueryParams.sqid);
  if (jfqueryParams.sqid == 0) {
    jfpageData.value = [];
    jftotal.value = 0;
    return;
  }
  loading.value = true;
  formData.xmmc = "";
  formData.xmbm = "";
  formData.jflb = "";
  formData.ysnd = "";
  ZxcgSqjfAPI.getPage(jfqueryParams)
    .then((data) => {
      console.log("数据：", data);
      jfpageData.value = data.list;
      jftotal.value = data.total;
      if (data.total > 0) {
        formData.xmmc = data.list[0].jfmc;
        formData.xmbm = data.list[0].jfbh;
        formData.jflb = data.list[0].jflb;
        formData.ysnd = data.list[0].jfnd;
        formData.xmfzr = "";
        formData.fzrbm = "";
      }
      //dcbmDisabled.value = total.value > 0;
    })
    .catch((error) => {
      console.error("获取经费卡和申请预算列表失败:", error);
      ElMessage.error("获取经费卡和申请预算列表失败");
      jfpageData.value = [];
      jftotal.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

/** 打开申请经费弹窗 */
const itemJfId = ref<number | undefined>();
const itemJfEditable = ref<boolean>(false);
//const handleOpenDialogYs = async (id?: number) => {
const handleOpenDialogYs = async (editable: boolean, row?: ZxcgSqjfPageVO) => {
  //await dataFormRef.value?.validate();
  if (formData.sqmc) {
    handleSaveSq().then(() => {
      itemJfId.value = row?.jfid;
      itemJfEditable.value = editable;
      choosedialog_ys.title = editable
        ? row?.jfid
          ? "修改经费卡和申请预算"
          : "新增经费卡和申请预算"
        : "查看经费卡和申请预算";
      choosedialog_ys.visible = true;
    });
  } else {
    ElMessage.error("采购项目名称不能为空");
  }
};

// 关闭弹窗
function handleCloseYsDialog() {
  choosedialog_ys.visible = false;
  itemJfId.value = undefined;
  if (ysRef.value) {
    //await
    ysRef?.value.handleClearForm();
  }

  //formData.id = undefined;
  // formData.status = 1;
}

function YsRefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    choosedialog_ys.visible = false;
  }
  jfhandlePageQuery();
}

const choosedialog_ys = reactive({
  title: "选择经费卡",
  visible: false,
});

const ysRef = ref<any | null>(null);

//新增预算
/*
const handleAddYs = async () => {
  if (ysRef.value) {
    await ysRef?.value.handleAddJf();
  }
};*/
//删除预算经费
const handleYsDelete = async (id?: number) => {
  if (id) {
    ZxcgSqjfAPI.deleteByIds(id.toString())
      .then((data) => {
        ElMessage.success("删除成功");
        jfhandlePageQuery();
      })
      .catch((error) => {
        console.error("删除经费卡和申请预算数据失败:", error);
        ElMessage.error("删除经费卡和申请预算数据失败");
      });
  } else {
  }
};

//--------------------------------申请明细---
const itemMxGuid = ref<string | undefined>();
const itemMxId = ref<number | undefined>();
const itemMxEditable = ref<boolean>(false);
const mxpageData = ref<ZxcgMxPageVO[]>([]); //明细列表
const mxtotal = ref(0);
//请求参数
const mxqueryParams = reactive<ZxcgMxPageQuery>({
  pageNum: 1,
  pageSize: 10,
  sqid: 0,
});
//列表查询
const mxhandlePageQuery = () => {
  if (formData.sqid) mxqueryParams.sqid = formData.sqid;
  else mxqueryParams.sqid = 0;
  if (mxqueryParams.sqid == 0) {
    mxpageData.value = [];
    mxtotal.value = 0;
    formData.ysje = 0;
    return;
  }
  loading.value = true;
  ZxcgMxAPI.getPage(mxqueryParams)
    .then((data) => {
      console.log("明细数据：", data);
      mxpageData.value = data.list;
      mxtotal.value = data.total;
      //dcbmDisabled.value = total.value > 0;
      //formData.ysje=0
      //获取明细总金额

      ZxcgMxAPI.getMxYsje(formData.sqid ? formData.sqid : 0)
        .then((data) => {
          console.log("明细总金额：", data);
          formData.ysje = data;
        })
        .catch((error) => {
          console.error("获取采购申请明细总金额失败:", error);
          ElMessage.error("获取采购申请明细总金额失败");
        })
        .finally(() => {});
    })
    .catch((error) => {
      console.error("获取采购申请明细列表失败:", error);
      ElMessage.error("获取采购申请明细列表失败");
      mxpageData.value = [];
      mxtotal.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

//const dataFormRef_Mx = ref(ElForm);
const choosedialog_mx = reactive({
  title: "采购申请明细",
  visible: false,
});
//const mxForm = reactive<ZxcgMxForm>({});
const handleOpenDialogMx = async (editable: boolean, row?: ZxcgMxPageVO) => {
  //await dataFormRef.value?.validate();
  if (editable) {
    var validate = true;
    if (formData.sqmc) {
    } else {
      validate = false;
      ElMessage.error("采购项目名称不能为空");
      return;
    }

    if (formData.cgml) {
    } else {
      validate = false;
      ElMessage.error("请选择采购目录");
    }

    if (validate) {
      const api = handleSaveSq();
      await api.then(() => {
        itemMxGuid.value = row?.guid || getGuid();
        itemMxId.value = row?.mxid;
        itemMxEditable.value = editable;
        choosedialog_mx.title = editable
          ? row?.mxid
            ? "修改采购申请明细"
            : "新增采购申请明细"
          : "查看采购申请明细";
        choosedialog_mx.visible = true;
      });
    }
  } else {
    itemMxGuid.value = row?.guid || getGuid();
    itemMxId.value = row?.mxid;
    itemMxEditable.value = editable;
    choosedialog_mx.title = "查看采购申请明细";
    choosedialog_mx.visible = true;
  }
};

//新增明细

const mxRef = ref<any | null>(null);
/*const handleAddMx = async () => {
  if (mxRef.value) {
    await mxRef?.value.handleAddMx();
  }
};*/
function MxRefreshFatherDrawer(close?: boolean) {
  //是否关闭弹窗由参数决定，默认不关闭
  if (close) {
    choosedialog_mx.visible = false;
  }
  mxhandlePageQuery();
}
// 关闭弹窗
function handleCloseMxDialog() {
  choosedialog_mx.visible = false;
  itemMxId.value = undefined;
  if (mxRef.value) {
    //await
    mxRef?.value.handleClearForm();
  }

  //formData.id = undefined;
  // formData.status = 1;
}
//删除明细
const handleMxDelete = async (id?: number) => {
  if (id) {
    ZxcgMxAPI.deleteByIds(id.toString())
      .then((data) => {
        ElMessage.success("删除成功");
        mxhandlePageQuery();
      })
      .catch((error) => {
        console.error("删除采购申请明细数据失败:", error);
        ElMessage.error("删除采购申请明细数据失败");
      });
  } else {
  }
};
//-----------------------------申请明细--

const fgw_A = ref(2000000);
const fgw_B = ref(4000000);
const fgw_C = ref(1000000);
const is_fgw = ref(false);
/** 初始化申请信息 */
function handleFormQuery(id?: number) {
  handleSqEdit(id);
  /*

  if (id) {
    ZxcgSqAPI.getFormData(id)
      .then((data) => {
        Object.assign(formData, data);
        console.log("zgbmData.list ;;", formData.zgbm);
        if (formData.zgbm) formData.zgbmlist = (formData.zgbm || "").split(",");
        //netcode.value = formData.netcode || "";
        // console.log("zgbmData.list ;;", zgbmData.list);
      })
      .catch((error) => {
        console.error("获取申请数据失败:", error);
        ElMessage.error("获取申请数据失败");
      });
  } else {
    // 进去前给个新的GUID

  }*/
}

//——————————————————————————————————————————————————form操作相关

const handleSaveSq = async () => {
  if (formData.sqmc) {
    if (formData.zgbmlist) formData.zgbm = formData.zgbmlist.join(",");
    else formData.zgbm = "";
    console.log("formData.zgbm", formData.zgbm);
    //处理显隐数据逻辑
    if (formData.isjk == "1") {
    } else {
      //去掉进口论证的论证
      formData.by7 = "";
    }
    if (formData.isdyly == "1") {
    } else {
      //去掉单一来源论证
      formData.dylzid = "";
    }
    //formData.by2
    if (formData.by2 == "0") {
    } else {
      //去掉不面向中小企业的理由
      formData.by9 = "";
    }
    if (formData.cgml == "A") {
    } else {
      //去掉新创分类
      formData.sfxcfl = "";
    }
    if (formData.sfxcfl == "1") {
    } else {
      //去掉是否新创
      formData.sfxc = "";
    }

    if (formData.by14 == "1") {
    } else {
      //去掉可行性分析
      formData.kxxfx = "";
      //去掉三重一大
      formData.sfszyd = "0";
    }

    if (formData.by16) {
    } else {
      formData.by16 = "0";
    }
    if (is_fgw) {
    } else {
      formData.sffgw = "0";
    }

    if (formData.sqcgfl == "xm") {
      formData.td = "0";
    } else if (formData.sqcgfl == "lx") {
      formData.td = "1";
    } else {
      formData.td = "";
    }
    if (formData.cgml == "B") {
    } else {
      formData.zbgcxm = "0";
    }
    if (formData.by14 == "1" && formData.cgml == "A") {
    } else {
      formData.sfzcy = "0";
    }

    if (formData.yjdnzfsj) formData.yjdnzfsj = formatLocalDateTime(new Date(formData.yjdnzfsj));
    // formData.xmmc="";
    console.log("formData.yjdnzfsj", formData.yjdnzfsj);
    const submitData = { ...formData };

    const id = formData.sqid;

    const apiCall = id ? ZxcgSqAPI.update(id, submitData) : ZxcgSqAPI.add(submitData);
    await apiCall
      .then(async (res) => {
        if (!res) {
          ElMessage.error("申请单保存失败");
        } else {
          ElMessage.success("申请单保存成功");
          //保存成功后，重新用id去查询一次数据
          formData.sqid = Number(res);
          // handleClearForm();
          handleFormQuery(formData.sqid);
          console.log("sqid:,", formData.sqid);
          keyId.value++;
          props.RefreshFatherDrawer();
        }
      })
      .catch((error) => {
        ElMessage.error("操作失败，失败原因：" + error.message);
      })
      .finally(() => {});
  } else {
    ElMessage.success("采购项目名称不能为空");
  }
};
//--------------以下是form表单相关
const dataFormRef = ref(ElForm);

// 采购申请表单数据
const formData = reactive<ZxcgSqForm>({});
/*const zgbmData = reactive({
  list: [] as string[],
});*/

const formType = reactive({
  add: false,
  edit: false,
  view: false,
});

// 采购申请表单校验规则
//const rules = reactive({});

const sqrules = reactive({
  sqmc: [{ required: true, message: "采购项目名称不能为空", trigger: "blur" }],
  bmfzr: [{ required: true, message: "部门审核人不能为空", trigger: "blur" }],
  by13: [{ required: true, message: "分管校领导不能为空", trigger: "blur" }],
  gzpdxq: [{ required: true, message: "派单校区不能为空", trigger: "blur" }],
  cgml: [{ required: true, message: "采购目录不能为空", trigger: "blur" }],
  fwlx: [{ required: true, message: "服务类别不能为空", trigger: "blur" }],
  zgbmlist: [{ required: true, message: "业务归口管理部门不能为空", trigger: "blur" }],
  yxmc: [{ required: true, message: "采购意向不能为空", trigger: "blur" }],
  yjdnzfsj: [{ required: true, message: "预计当年支付时间不能为空", trigger: "blur" }],
  yjdnzfje: [
    { required: true, message: "预计当年支付金额不能为空", trigger: "blur" },
    {
      pattern: /^(?=.*[1-9])\d*(?:\.\d{1,2})?$/,
      message: "必须为大于0的数字",
      trigger: "blur",
    },
  ],
  isjk: [{ required: true, message: "是否进口不能为空", trigger: "blur" }],
  //by7: [{ required: true, message: "选取进口专家论证意见或在采购申请附件中上传", trigger: "blur" }],
  isdyly: [{ required: true, message: "是否单一来源", trigger: "blur" }],
  //dylzid: [{ required: true, message: "选取单一来源专家论证意见或在采购申请附件中上传", trigger: "blur" }],
  by2: [{ required: true, message: "是否面向中小企业不能为空", trigger: "blur" }],
  by9: [{ required: true, message: "不面向中小企业的理由不能为空", trigger: "blur" }],
  by17: [{ required: true, message: "是否学科办经费不能为空", trigger: "blur" }],
  sffgw: [{ required: true, message: "是否发改委项目不能为空", trigger: "blur" }],
  iskysb: [{ required: true, message: "是否科研仪器设备不能为空", trigger: "blur" }],
  sfxcfl: [{ required: true, message: "是否信创不能为空", trigger: "blur" }],
  sfxc: [{ required: true, message: "是否信创不能为空", trigger: "blur" }],
  zbgcxm: [{ required: true, message: "是否必须招标的工程项目不能为空", trigger: "blur" }],
  sfszyd: [{ required: true, message: "是否本单位三重一大事项不能为空", trigger: "blur" }],
  sfzcy: [{ required: true, message: "是否政采云采购不能为空", trigger: "blur" }],
  kxxfx: [{ required: true, message: "采购需求不能为空", trigger: "blur" }],
});

/** 提交采购申请表单 */
function handleSubmitSq() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      handleSaveSq().then(async () => {
        //第二层保存完成
        const submitData = { ...formData };
        const dialogloading = ElLoading.service({
          lock: true,
          text: "处理中",
        });

        //工作流提交
        await ZxcgSqAPI.submit(submitData.guid || "")
          .then(() => {
            //第三层提交完成
            ElMessage.success("采购申请提交成功");
            props.RefreshFatherDrawer(true);
          })
          .finally(() => {
            dialogloading.close();
            loading.value = false;
          })
          .catch((error) => {
            ElMessage.error(error.message);
          });
      });
    }
  });
}
watch(
  [() => formData.ysje, () => formData.cgml],
  ([newysje, newcgml]) => {
    is_fgw.value = false;
    if (formData.ysje && formData.cgml) {
      var ysje = formData.ysje ? formData.ysje : 0;
      if (formData.cgml == "A") {
        if (ysje >= fgw_A.value) {
          is_fgw.value = true;
        }
      } else if (formData.cgml == "B") {
        if (ysje >= fgw_B.value) {
          is_fgw.value = true;
        }
      } else if (formData.cgml == "C") {
        if (ysje >= fgw_C.value) {
          is_fgw.value = true;
        }
      }
    }
  },
  { immediate: true }
);

onMounted(() => {
  /*handleFormQuery(props.id);
  handlePageQuery();*/
  if (props.editable) handleSqAdd(props.sqcgfl, props.sqid);
  else handleFormQuery(props.sqid);
});
// 明确暴露给父组件的方法
defineExpose({
  handleClearForm,
});
</script>
<style lang="scss" scoped>
/* 自定义对话框样式 */
.custom-dialog {
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;

  .el-dialog__header {
    background-color: #409eff;
    color: #ffffff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;

    .el-dialog__title {
      color: #ffffff;
    }
  }

  .el-dialog__body {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
