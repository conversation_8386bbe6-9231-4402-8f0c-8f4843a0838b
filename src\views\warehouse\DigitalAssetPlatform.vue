<template>
  <div class="digital-asset-platform-container">
    <!-- 头部区域：标题、导航 -->
    <div class="header-wrapper">
      <div class="header">
        <div class="header-left">
          <img :src="gykjLogo" alt="中国美术学院 logo" class="gykj-logo" />
          <h1 class="title">数字化资产管理平台</h1>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-demo"
          mode="horizontal"
          background-color="transparent"
          text-color="#333"
          active-text-color="#409EFF"
          @select="handleMenuSelect"
        >
          <el-menu-item index="1">
            <el-icon><HomeFilled /></el-icon>
            首页
          </el-menu-item>
          <el-menu-item index="2">
            <el-icon><Bell /></el-icon>
            通知公告
          </el-menu-item>
          <el-menu-item index="3">
            <el-icon><Setting /></el-icon>
            系统管理
          </el-menu-item>
          <el-menu-item index="4">
            <el-icon><User /></el-icon>
            <el-icon class="ml-5"><SwitchButton /></el-icon>
          </el-menu-item>
        </el-menu>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 功能模块区域 -->
      <div class="modules-container">
        <!-- 资产管理系统 -->
        <div class="module-card" @click="navigateToModule('asset-management')">
          <div class="module-icon">
            <img :src="assetManagementIcon" alt="资产管理系统" class="icon-image" />
          </div>
          <div class="module-content">
            <h3 class="module-title">资产管理系统</h3>
            <p class="module-description">资产登记、资产台账、资产使用、资产调拨、资产处置等</p>
            <el-button type="primary" class="module-button">点击进入 >></el-button>
          </div>
        </div>

        <!-- 物品管理系统 -->
        <div class="module-card" @click="navigateToModule('goods-management')">
          <div class="module-icon">
            <img :src="goodsManagementIcon" alt="物品管理系统" class="icon-image" />
          </div>
          <div class="module-content">
            <h3 class="module-title">物品管理系统</h3>
            <p class="module-description">低值品、易耗品、材料的入库和领用管理等</p>
            <el-button type="primary" class="module-button">点击进入 >></el-button>
          </div>
        </div>

        <!-- 资产公物仓 -->
        <div class="module-card" @click="navigateToModule('asset-warehouse')">
          <div class="module-icon">
            <img :src="assetWarehouseIcon" alt="资产公物仓" class="icon-image" />
          </div>
          <div class="module-content">
            <h3 class="module-title">资产公物仓</h3>
            <p class="module-description">
              资产入仓、资产申请（出仓）、资产盘点、公物仓数据分析和统计等
            </p>
            <el-button type="primary" class="module-button">点击进入 >></el-button>
          </div>
        </div>

        <!-- 资产驾驶舱 -->
        <div class="module-card" @click="navigateToModule('asset-dashboard')">
          <div class="module-icon">
            <img :src="assetDashboardIcon" alt="资产驾驶舱" class="icon-image" />
          </div>
          <div class="module-content">
            <h3 class="module-title">资产驾驶舱</h3>
            <p class="module-description">
              资产总览、资产分类统计、资产变动统计、资产警示、资产排行等
            </p>
            <el-button type="primary" class="module-button">点击进入 >></el-button>
          </div>
        </div>
      </div>

      <!-- 侧边栏信息区域 -->
      <div class="sidebar-container">
        <!-- 通知公告 -->
        <el-card class="sidebar-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Bell /></el-icon>
              <span>通知公告</span>
              <el-button type="text" size="small" @click="viewAllNotices">查看更多 ></el-button>
            </div>
          </template>
          <div class="notice-list">
            <div
              v-for="(notice, index) in noticeList"
              :key="index"
              class="notice-item"
              @click="viewNoticeDetail(notice)"
            >
              <div class="notice-tag" :class="notice.type">
                {{ getNoticeTypeLabel(notice.type) }}
              </div>
              <div class="notice-content">
                <div class="notice-title">{{ notice.title }}</div>
                <div class="notice-date">{{ notice.date }}</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 系统信息 -->
        <el-card class="sidebar-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>系统信息</span>
              <el-button type="text" size="small" @click="downloadFile">资料下载</el-button>
            </div>
          </template>
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">当前用户数量：</span>
              <span class="info-value">{{ systemInfo.userCount }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">资产总数：</span>
              <span class="info-value">{{ systemInfo.assetCount }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">今日新增：</span>
              <span class="info-value">{{ systemInfo.todayNew }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span class="info-value">{{ systemInfo.version }}</span>
            </div>
          </div>
        </el-card>

        <!-- 快捷操作 -->
        <el-card class="sidebar-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Operation /></el-icon>
              <span>快捷操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <div class="action-item" @click="quickAction('asset-register')">
              <el-icon><Plus /></el-icon>
              <span>资产登记</span>
            </div>
            <div class="action-item" @click="quickAction('asset-search')">
              <el-icon><Search /></el-icon>
              <span>资产查询</span>
            </div>
            <div class="action-item" @click="quickAction('asset-report')">
              <el-icon><Document /></el-icon>
              <span>报表导出</span>
            </div>
            <div class="action-item" @click="quickAction('system-backup')">
              <el-icon><Download /></el-icon>
              <span>数据备份</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="footer-copyright">
      <div class="footer-container">
        <img :src="gykjLogo" alt="中国美术学院" class="footer-logo" />
        <div class="divider"></div>
        <div class="copyright-content">
          <div class="copyright-text">
            <el-icon><Location /></el-icon>
            版权所有：中国美术学院
          </div>
          <div class="copyright-text">
            <el-icon><MapLocation /></el-icon>
            地址：杭州市上城区南山路218号
          </div>
          <div class="copyright-text">
            <el-icon><Service /></el-icon>
            技术支持：浙江工越信息科技有限公司
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  HomeFilled,
  Bell,
  Setting,
  User,
  SwitchButton,
  Monitor,
  Operation,
  Plus,
  Search,
  Document,
  Download,
  Location,
  MapLocation,
  Service,
} from "@element-plus/icons-vue";
import router from "@/router";
import gykjLogo from "@/assets/GYKJlogo.png";
import assetManagementIconSrc from "@/assets/icons/asset-management.svg";
import goodsManagementIconSrc from "@/assets/icons/goods-management.svg";
import assetWarehouseIconSrc from "@/assets/icons/asset-warehouse.svg";
import assetDashboardIconSrc from "@/assets/icons/asset-dashboard.svg";

// 当前激活的菜单
const activeMenu = ref("1");

// 模块图标
const assetManagementIcon = ref(assetManagementIconSrc);
const goodsManagementIcon = ref(goodsManagementIconSrc);
const assetWarehouseIcon = ref(assetWarehouseIconSrc);
const assetDashboardIcon = ref(assetDashboardIconSrc);

// 通知公告列表
const noticeList = reactive([
  {
    id: 1,
    type: "urgent",
    title: "【202001172】资产处置申报",
    date: "2024-01-15",
  },
  {
    id: 2,
    type: "normal",
    title: "【202001171】资产处置申报",
    date: "2024-01-14",
  },
  {
    id: 3,
    type: "normal",
    title: "【202001168】资产处置申报",
    date: "2024-01-13",
  },
  {
    id: 4,
    type: "normal",
    title: "【202001165】资产处置申报",
    date: "2024-01-12",
  },
  {
    id: 5,
    type: "normal",
    title: "【202001164】资产处置申报",
    date: "2024-01-11",
  },
  {
    id: 6,
    type: "normal",
    title: "【202001163】资产处置申报",
    date: "2024-01-10",
  },
  {
    id: 7,
    type: "normal",
    title: "【202001162】资产处置申报",
    date: "2024-01-09",
  },
  {
    id: 8,
    type: "normal",
    title: "【202001159】资产处置申报",
    date: "2024-01-08",
  },
  {
    id: 9,
    type: "normal",
    title: "【202001158】资产处置申报",
    date: "2024-01-07",
  },
  {
    id: 10,
    type: "normal",
    title: "【202001157】资产处置申报",
    date: "2024-01-06",
  },
]);

// 系统信息
const systemInfo = reactive({
  userCount: "1,234",
  assetCount: "56,789",
  todayNew: "23",
  version: "v2.1.0",
});

/**
 * 菜单选择处理
 */
const handleMenuSelect = (index: string) => {
  activeMenu.value = index;
};

/**
 * 导航到模块
 */
const navigateToModule = (module: string) => {
  switch (module) {
    case "asset-management":
      ElMessage.info("跳转到资产管理系统");
      // router.push("/asset-management");
      break;
    case "goods-management":
      ElMessage.info("跳转到物品管理系统");
      // router.push("/goods-management");
      break;
    case "asset-warehouse":
      router.push("/warehouse");
      break;
    case "asset-dashboard":
      ElMessage.info("跳转到资产驾驶舱");
      // router.push("/asset-dashboard");
      break;
    default:
      ElMessage.warning("功能开发中...");
  }
};

/**
 * 获取通知类型标签
 */
const getNoticeTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    urgent: "紧急",
    normal: "通知",
    info: "信息",
  };
  return typeMap[type] || "通知";
};

/**
 * 查看通知详情
 */
const viewNoticeDetail = (notice: any) => {
  ElMessage.info(`查看通知：${notice.title}`);
};

/**
 * 查看所有通知
 */
const viewAllNotices = () => {
  ElMessage.info("查看所有通知");
};

/**
 * 下载文件
 */
const downloadFile = () => {
  ElMessage.info("下载系统资料");
};

/**
 * 快捷操作
 */
const quickAction = (action: string) => {
  const actionMap: Record<string, string> = {
    "asset-register": "资产登记",
    "asset-search": "资产查询",
    "asset-report": "报表导出",
    "system-backup": "数据备份",
  };
  ElMessage.info(`执行操作：${actionMap[action]}`);
};

// 页面加载时初始化数据
onMounted(() => {
  // 可以在这里加载一些初始数据
});
</script>

<style lang="scss" scoped>
.digital-asset-platform-container {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}

// 头部样式
.header-wrapper {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.header {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
}

.gykj-logo {
  height: 40px;
  margin-right: 15px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin: 0;
}

.el-menu-demo {
  border-bottom: none;
  background-color: transparent;
}

// 主内容区域
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 20px;
  display: flex;
  gap: 30px;
  min-height: auto;
}

// 模块容器
.modules-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

// 模块卡片
.module-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-5px);
    border-color: #dcdfe6;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    
    .module-icon {
      background: #e6f1ff;
      
      .icon-image {
        filter: invert(39%) sepia(60%) saturate(1552%) hue-rotate(182deg) brightness(100%) contrast(96%);
        transform: scale(1.05);
      }
    }
  }

  .module-icon {
    width: 80px;
    height: 80px;
    background: #f0f2f5;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;

    .icon-image {
      width: 50px;
      height: 50px;
      object-fit: contain;
      filter: invert(39%) sepia(40%) saturate(1352%) hue-rotate(182deg) brightness(92%) contrast(96%);
    }
  }

  .module-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .module-title {
      font-size: 24px;
      font-weight: 600;
      color: #2c5aa0;
      margin: 0;
    }

    .module-description {
      font-size: 16px;
      color: #666;
      margin: 0;
      line-height: 1.5;
    }

    .module-button {
      align-self: flex-start;
      margin-top: 10px;
      background: #409eff;
      border: none;
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;
      transition: all 0.3s ease;
      box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);

      &:hover {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        transform: translateY(-2px);
      }
    }
  }
}

// 侧边栏容器
.sidebar-container {
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex-shrink: 0;
}

// 侧边栏卡片
.sidebar-card {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  :deep(.el-card__header) {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  :deep(.el-card__body) {
    padding: 20px;
  }

  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #303133;

    .el-button {
      margin-left: auto;
      color: #409eff;
    }
  }
}

// 通知列表
.notice-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;

  .notice-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(64, 158, 255, 0.05);
      border-radius: 6px;
      padding: 8px 10px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }

    .notice-tag {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      color: white;
      flex-shrink: 0;

      &.urgent {
        background-color: #f56c6c;
      }

      &.normal {
        background-color: #409eff;
      }

      &.info {
        background-color: #909399;
      }
    }

    .notice-content {
      flex: 1;
      min-width: 0;

      .notice-title {
        font-size: 14px;
        color: #303133;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .notice-date {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }
}

// 系统信息
.system-info {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      font-size: 14px;
      color: #606266;
    }

    .info-value {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
  }
}

// 快捷操作
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    background-color: rgba(64, 158, 255, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);

    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
    }

    .el-icon {
      font-size: 20px;
      color: #409eff;
    }

    span {
      font-size: 12px;
      color: #606266;
      text-align: center;
    }
  }
}

// 底部版权信息
.footer-copyright {
  background: #f5f7fa;
  padding: 20px 0;
  margin-top: 0;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
}

.footer-logo {
  height: 40px;
  margin-right: 20px;
}

.divider {
  width: 1px;
  height: 40px;
  background-color: #dcdfe6;
  margin: 0 20px;
}

.copyright-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.copyright-text {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;

  .el-icon {
    margin-right: 5px;
    color: #909399;
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: 20px;
  }

  .sidebar-container {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px 10px;
  }

  .module-card {
    flex-direction: column;
    text-align: center;
    padding: 20px;

    .module-icon {
      width: 60px;
      height: 60px;
    }

    .module-content {
      .module-title {
        font-size: 20px;
      }

      .module-description {
        font-size: 14px;
      }
    }
  }

  .sidebar-container {
    grid-template-columns: 1fr;
  }
}
</style>
