import request from "@/utils/request";

const GWC_HALL_BASE_URL = "/api/v1/gwc/hall";

const GWC_SYB_BASE_URL = "/api/v1/gwcSybs";
/**
 * 公物仓资产查询参数
 */
export interface GwcHallQuery extends PageQuery {
  /** 资产名称 */
  searchKeyword?: string;

  rklx?: string;
}

/**
 * 公物仓资产信息
 */
export interface GwcHallVO {
  id: number;
  /** 唯一标识符 */
  guid: string;
  /** 使用人 */
  syr: string;
  /** 图片路径 */
  tpj: string;
  /** 已使用天数 */
  ysyqs: number;
  /** 资产编号 */
  zcbh: string;
  /** 资产名称 */
  zcmc: string;
  /** 品牌 */
  pp: string;
  /** 规格型号 */
  ggxh: string;
  /** 确定日期 */
  qdrq: string;
  /** 使用部门 */
  sybm: string;
  /** 公物仓状态 */
  gwczt: string;
  /** 入库状态 */
  rkzt: string;
  /** 入仓时间 */
  rcsj: string;
  /** 使用性质 */
  syxz: string;
  /** 资产类型编号 */
  zclxbh: string;
  /** 资产金额 */
  je: number;
}

/**
 * 固定资产验收单相关API
 *
 * @property {function} getPage 获取验收单分页数据
 * @property {function} getAssetCount 获取资产类型统计数量
 * @property {function} saveGwcSyb 保存固定资产验收单数据
 */
const gwcAPI = {
  /** 获取验收单分页数据 */
  getPage(queryParams: any) {
    return request<any, PageResult<GwcHallVO[]>>({
      url: `${GWC_HALL_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  },

  getAssetCount() {
    return request<any, any>({
      url: `${GWC_HALL_BASE_URL}/countByType`,
      method: "get",
    });
  },

  getAssetCountByReceive() {
    return request<any, number>({
      url: `${GWC_HALL_BASE_URL}/countByReceive`,
      method: "get",
    });
  },

  saveGwcSyb(ids: string, sqlx: string, iszc: string, syguid: string) {
    return request<any, any>({
      url: `${GWC_SYB_BASE_URL}/add/${ids}/${sqlx}/${iszc}/${syguid}`,
      method: "post",
    });
  },
};

export default gwcAPI;
