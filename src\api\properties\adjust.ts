import request from "@/utils/request";

/**
 * 调剂单查询参数
 */
export interface adjustPageQuery extends PageQuery {
  /** 调剂单号 */
  sqdh?: string;
  /** 数据状态 0-草稿, 1-已提交, 9-已取消 */
  sjzt?: string;
  /** 使用部门 */
  sybm?: string;
  /** 使用人 */
  syr?: string;
  /** 登记时间 */
  djsj?: string[];
  //查询类型，是新增页审核页还是查询页
  type?: string;
  //查询代办/已办
  checkstatus?: number;
}

const adjust_BASE_URL = "/api/v1/store";

const adjustAPI = {
  /**
   * 获取调剂单列表
   * @param params 查询参数
   * @returns 调剂单列表
   */
  getPage(queryParams?: adjustPageQuery): Promise<PageResult<any[]>> {
    return request<any, PageResult<any[]>>({
      url: `${adjust_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取调剂单详情
   * @param id 调剂单ID
   * @returns 调剂单详情
   */
  getAdjustDetail(id: number): Promise<any> {
    return request({
      url: `/properties/adjust/${id}`,
      method: "get",
    });
  },

  /**
   * 新增或修改调剂单
   * @param data 调剂单数据
   * @returns 操作结果
   */
  saveAdjust(data: { id?: number; [key: string]: any }): Promise<any> {
    return request({
      url: "/properties/adjust",
      method: data.id ? "put" : "post",
      data,
    });
  },

  /**
   * 删除调剂单
   * @param ids 调剂单ID数组
   * @returns 操作结果
   */
  deleteAdjust(ids: string): Promise<any> {
    return request({
      url: `/properties/adjust/${ids}`,
      method: "delete",
    });
  },

  /**
   * 提交调剂单进行审批
   * @param id 调剂单ID
   * @returns 操作结果
   */
  submitAdjust(id: number): Promise<any> {
    return request({
      url: `/properties/adjust/submit/${id}`,
      method: "put",
    });
  },

  /**
   * 取消调剂单审批
   * @param id 调剂单ID
   * @returns 操作结果
   */
  cancelAdjust(id: number): Promise<any> {
    return request({
      url: `/properties/adjust/cancel/${id}`,
      method: "put",
    });
  },

  /**
   * 获取可调剂资产列表
   * @param params 查询参数
   * @returns 可调剂资产列表
   */
  getAdjustableAssets(params: any): Promise<PageResult<any[]>> {
    return request<any, PageResult<any[]>>({
      url: "/properties/adjust/assets",
      method: "get",
      params,
    });
  },

  /**
   * 审批调剂单
   * @param data 审批数据
   * @returns 操作结果
   */
  approveAdjust(data: any): Promise<any> {
    return request({
      url: "/properties/adjust/approve",
      method: "post",
      data,
    });
  },

  /**
   * 驳回调剂单
   * @param data 驳回数据
   * @returns 操作结果
   */
  rejectAdjust(data: any): Promise<any> {
    return request({
      url: "/properties/adjust/reject",
      method: "post",
      data,
    });
  },

  /**
   * 获取调剂单流程记录
   * @param id 调剂单ID
   * @returns 流程记录列表
   */
  getAdjustFlowRecords(id: any): Promise<any> {
    return request({
      url: `/properties/adjust/flow/${id}`,
      method: "get",
    });
  },
};
export default adjustAPI;
