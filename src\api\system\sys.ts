import request from "@/utils/request";

const USER_BASE_URL = "/api/v1/sys";

const SysAPI = {
  /**
   * 获取用户分页列表
   *
   * @param queryParams 查询参数
   */
  getUserList(queryParams: UserListQuery) {
    return request<any, PageResult<UserPageVO[]>>({
      url: `${USER_BASE_URL}/get-userlist`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取部门
   *
   * @param queryParams 查询参数
   */
  getDeptList(queryParams: DeptListQuery) {
    return request<any, PageResult<DeptPageVO[]>>({
      url: `${USER_BASE_URL}/get-deptlist`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 取Xcode
   *
   * @param queryParams 查询参数
   */
  getXcodeList(queryParams: XCodeListQuery) {
    return request<any, PageResult<XCodeVO[]>>({
      url: `${USER_BASE_URL}/get-xcodelist`,
      method: "get",
      params: queryParams,
    });
  } /**
   * 取Xcode
   *
   * @param queryParams 查询参数
   */,
  getXcodeListByCode(code: string) {
    return request<any, XCodeVO[]>({
      url: `${USER_BASE_URL}/xcode/list`,
      method: "get",
      params: { code: code },
    });
  },
};

export default SysAPI;

/**
 * 用户分页查询对象
 */
export interface UserListQuery extends PageQuery {
  /** 用户名	 */
  username?: string;

  /** 角色编码	 */
  rcode?: string;

  /** 用户号	 */
  ucode?: string;

  /** 部门编码	 */
  dcode?: string;
}

/** 用户分页对象 */
export interface UserPageVO {
  /** 用户ID */
  id: number;
  /** 用户头像URL */
  avatar?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 部门名称 */
  deptName?: string;
  /** 用户邮箱 */
  email?: string;
  /** 性别 */
  gender?: number;
  /** 手机号 */
  mobile?: string;
  /** 用户昵称 */
  nickname?: string;
  /** 角色名称，多个使用英文逗号(,)分割 */
  roleNames?: string;
  /** 用户状态(1:启用;0:禁用) */
  status?: number;
  /** 用户名 */
  username?: string;
}

export interface DeptListQuery extends PageQuery {
  /** 部门编码	 */
  boobm: string;
  /** 父部门编码	 */
  preBoobm?: string;
}

export interface DeptPageVO {
  boobm?: string;
  booid?: number;
  boolastbm?: string;
  boomc?: string;
  boojc?: string;
  boolb?: string;
  booznms?: string;
  boofxdj?: string;
  boofxms?: string;
  boofkcs?: string;
  remark?: string;
  boosyzt?: string;
  booxh?: number;
  booczrq?: string;
  boobgzt?: string;
  booczz?: string;
  fgxld?: string;
  bmfzr?: string;
}

export interface XCodeListQuery extends PageQuery {
  /** 父部门编码	 */
  preXCode?: string;
}

export interface XCodeVO {
  id: number;
  xcode: string;
  ycode?: string;
  coderule?: string;
  name: string;
  dataflag?: string;
  sort?: number;
  remark?: string;
  flag?: string;
  notes?: string;
  by1?: string;
  by2?: string;
  by3?: string;
  mburl?: string;
}
