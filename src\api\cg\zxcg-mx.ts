import request from "@/utils/request";

const ZXCGMX_BASE_URL = "/api/v1/zxcgMxs";

const ZxcgMxAPI = {
  /** 获取采购申请明细分页数据 */
  getPage(queryParams?: ZxcgMxPageQuery) {
    return request<any, PageResult<ZxcgMxPageVO[]>>({
      url: `${ZXCGMX_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取采购申请明细表单数据
   *
   * @param id ZxcgMxID
   * @returns ZxcgMx表单数据
   */
  getFormData(id: number) {
    return request<any, ZxcgMxForm>({
      url: `${ZXCGMX_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加采购申请明细*/
  add(data: ZxcgMxForm) {
    return request({
      url: `${ZXCGMX_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新采购申请明细
   *
   * @param id ZxcgMxID
   * @param data ZxcgMx表单数据
   */
  update(id: number, data: ZxcgMxForm) {
    return request({
      url: `${ZXCGMX_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除采购申请明细，多个以英文逗号(,)分割
   *
   * @param ids 采购申请明细ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${ZXCGMX_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
  /**
   * 获取明细合计金额
   *
   * @param ids 采购申请明细ID字符串，多个以英文逗号(,)分割
   * @returns ysje
   */
  getYsje(ids: string) {
    return request({
      url: `${ZXCGMX_BASE_URL}/ysje/${ids}`,
      method: "get",
    });
  },
  /**
   * 获取明细合计金额
   *
   * @param sqid 采购申请id
   * @returns ysje
   */
  getMxYsje(sqid: number) {
    return request<any, number>({
      url: `${ZXCGMX_BASE_URL}/mxysje/${sqid}`,
      method: "get",
    });
  },
};

export default ZxcgMxAPI;

/** 采购申请明细分页查询参数 */
export interface ZxcgMxPageQuery extends PageQuery {
  /** 母表GUID|hidden */
  parentguid?: string;
  /** 外键，自增|hidden */
  sqid?: number;
  /** 明细编号 */
  mxbh?: string;
  /** 物品名称 */
  wpmc?: string;
  /** rwid|hidden */
  rwid?: number;
  /** wtid|hidden */
  wtid?: number;
  /** wjid|hidden */
  wjid?: number;
  /** bdid|hidden */
  bdid?: number;
}

/** 采购申请明细表单对象 */
export interface ZxcgMxForm {
  /** 采购明细表|hidden */
  mxid?: number;
  /** guid|hidden */
  guid?: string;
  /** 母表GUID|hidden */
  parentguid?: string;
  /** 外键，自增|hidden */
  sqid?: number;
  /** 类别|hidden */
  type?: string;
  /** 预算明细|hidden */
  ysmx?: number;
  /** 明细编号 */
  mxbh?: string;
  /** 物品名称 */
  wpmc?: string;
  /** 采购目录 */
  cgml?: string;
  /*采购目录名称 */
  cgmlname?: string;
  /** 品牌厂商 */
  ppcs?: string;
  /** 规格 */
  gg?: string;
  /** 详细参数 */
  xxcs?: string;
  /** 是否特殊 进口、辐射、其它|hidden */
  ists?: string;
  /** 单位 */
  dw?: string;
  dwname?: string;
  /** 数量 */
  sl?: number;
  /** 单价（元） */
  price?: number;
  /** 金额（元） */
  ysje?: number;
  /** 开始时间|hidden */
  time1?: Date;
  /** 结束时间|hidden */
  time2?: Date;
  /** 其他说明|hidden */
  notes?: string;
  /** 供应商|hidden */
  gys?: string;
  /** 组织机构代码|hidden */
  jgdm?: string;
  /** 法人|hidden */
  gysfr?: string;
  /** 法人身份证|hidden */
  frsfz?: string;
  /** 供应商联系人|hidden */
  gyslxr?: string;
  /** 联系电话|hidden */
  gystel?: string;
  /** 资产类型（0资产，1非资产）|hidden */
  khyh?: string;
  /** 开户帐号|hidden */
  khzh?: string;
  /** 采购数量|hidden */
  xsl?: number;
  /** 成交价格(元)|hidden */
  cjjg?: number;
  /** 是否备案（0 否，1是）|hidden */
  isba?: string;
  /** rwid|hidden */
  rwid?: number;
  /** wtid|hidden */
  wtid?: number;
  /** wmwtid|hidden */
  wmwtid?: number;
  /** wjid|hidden */
  wjid?: number;
  /** baid|hidden */
  baid?: number;
  /** bdid|hidden */
  bdid?: number;
  /** 明细状态 0执行，1完成|hidden */
  mxzt?: string;
  /** 用途？|hidden */
  yt?: string;
  /** 使用人|hidden */
  syr?: string;
  /** 存放地点|hidden */
  cfdd?: string;
  /** 协议数量？|hidden */
  xysl?: number;
  /** 申购理由 */
  sgly?: string;
  /** 成交价？|hidden */
  cjprice?: number;
  /** 是否进口 */
  sfjk?: number;
  /** 预算编码 */
  ysbm?: string;
  /** 是否超标 0否 1是 （可以超出采购目录限额的金额） */
  iscb?: number;
  /** 审定金额|hidden */
  sdje?: number;
  /** 核减理由|hidden */
  hjly?: string;
  /** 费用项 */
  bCode?: string;
  /** 预算项 */
  buCode?: string;
  /** 材料类型管理|hidden */
  by1?: string;
  /** 材料类型管理|hidden */
  xh?: string;
  /** 政采云链接地址 */
  zcydz?: string;
  /** 用款指标ID（BudgetId） */
  by2?: string;
  /** 用款明细指标ID（DetailId） */
  by3?: string;
  /** 采购方式适用的法律条款代码 */
  fltk?: string;
  /** 确认书文号 */
  qrswh?: string;
  /** 项目优先级程度0普通  1 紧急 */
  yxj?: string;
  /** 备案标志0 不备案 1 外网备案 2 内网备案 */
  babz?: string;
  /** 是否保密0 保密  1 普通 */
  sfbm?: string;
  /** 付款方式 */
  by5?: string;
  /** 资金类型 */
  by4?: string;
  /** 政采云购物车过来的数据 */
  by9?: string;
  /** 进口理由 */
  jkly?: string;
  /** 浙江省财建议书上报结果 0失败 1成功 */
  sbjg?: string;
  /** 上报结果说明 */
  sbjgsm?: string;
  /** 询价，竞价表id，合并明细使用 */
  zxid?: number;
  /** 意向编号 */
  yxbh?: string;
  /** 确认书编号 */
  qrsbh?: string;
  /** 被拆分的明细的guid,如果不为空，表示已经被拆分过了 */
  cfguid?: string;
  /** 是否变更 */
  isbg?: string;
  /** 明细订单编号（冻结解冻） */
  mxordbh?: string;
  /** 网超推荐id */
  wcid?: number;
  /** 经贸用的，作废 */
  cfmx?: string;
  /** 经贸用的，作废 */
  ysjf?: string;
  /** 大确认书编号 */
  dqrsbh?: string;
  /** base_product.guid */
  cpguid?: string;
}

/** 采购申请明细分页对象 */
export interface ZxcgMxPageVO {
  /** 采购明细表|hidden */
  mxid?: number;
  /** guid|hidden */
  guid?: string;
  /** 母表GUID|hidden */
  parentguid?: string;
  /** 外键，自增|hidden */
  sqid?: number;
  /** 类别|hidden */
  type?: string;
  /** 预算明细|hidden */
  ysmx?: number;
  /** 明细编号 */
  mxbh?: string;
  /** 物品名称 */
  wpmc?: string;
  /** 采购目录 */
  cgml?: string;

  /** 品牌厂商 */
  ppcs?: string;
  /** 规格 */
  gg?: string;
  /** 详细参数 */
  xxcs?: string;
  /** 是否特殊 进口、辐射、其它|hidden */
  ists?: string;
  /** 单位 */
  dw?: string;
  /** 数量 */
  sl?: number;
  /** 单价（元） */
  price?: number;
  /** 金额（元） */
  ysje?: number;
  /** 开始时间|hidden */
  time1?: Date;
  /** 结束时间|hidden */
  time2?: Date;
  /** 其他说明|hidden */
  notes?: string;
  /** 供应商|hidden */
  gys?: string;
  /** 组织机构代码|hidden */
  jgdm?: string;
  /** 法人|hidden */
  gysfr?: string;
  /** 法人身份证|hidden */
  frsfz?: string;
  /** 供应商联系人|hidden */
  gyslxr?: string;
  /** 联系电话|hidden */
  gystel?: string;
  /** 资产类型（0资产，1非资产）|hidden */
  khyh?: string;
  /** 开户帐号|hidden */
  khzh?: string;
  /** 采购数量|hidden */
  xsl?: number;
  /** 成交价格(元)|hidden */
  cjjg?: number;
  /** 是否备案（0 否，1是）|hidden */
  isba?: string;
  /** rwid|hidden */
  rwid?: number;
  /** wtid|hidden */
  wtid?: number;
  /** wmwtid|hidden */
  wmwtid?: number;
  /** wjid|hidden */
  wjid?: number;
  /** baid|hidden */
  baid?: number;
  /** bdid|hidden */
  bdid?: number;
  /** 明细状态 0执行，1完成|hidden */
  mxzt?: string;
  /** 用途？|hidden */
  yt?: string;
  /** 使用人|hidden */
  syr?: string;
  /** 存放地点|hidden */
  cfdd?: string;
  /** 协议数量？|hidden */
  xysl?: number;
  /** 申购理由 */
  sgly?: string;
  /** 成交价？|hidden */
  cjprice?: number;
  /** 是否进口 */
  sfjk?: number;
  /** 预算编码 */
  ysbm?: string;
  /** 是否超标 0否 1是 （可以超出采购目录限额的金额） */
  iscb?: number;
  /** 审定金额|hidden */
  sdje?: number;
  /** 核减理由|hidden */
  hjly?: string;
  /** 费用项 */
  bCode?: string;
  /** 预算项 */
  buCode?: string;
  /** 材料类型管理|hidden */
  by1?: string;
  /** 材料类型管理|hidden */
  xh?: string;
  /** 政采云链接地址 */
  zcydz?: string;
  /** 用款指标ID（BudgetId） */
  by2?: string;
  /** 用款明细指标ID（DetailId） */
  by3?: string;
  /** 采购方式适用的法律条款代码 */
  fltk?: string;
  /** 确认书文号 */
  qrswh?: string;
  /** 项目优先级程度0普通  1 紧急 */
  yxj?: string;
  /** 备案标志0 不备案 1 外网备案 2 内网备案 */
  babz?: string;
  /** 是否保密0 保密  1 普通 */
  sfbm?: string;
  /** 付款方式 */
  by5?: string;
  /** 资金类型 */
  by4?: string;
  /** 政采云购物车过来的数据 */
  by9?: string;
  /** 进口理由 */
  jkly?: string;
  /** 浙江省财建议书上报结果 0失败 1成功 */
  sbjg?: string;
  /** 上报结果说明 */
  sbjgsm?: string;
  /** 询价，竞价表id，合并明细使用 */
  zxid?: number;
  /** 意向编号 */
  yxbh?: string;
  /** 确认书编号 */
  qrsbh?: string;
  /** 被拆分的明细的guid,如果不为空，表示已经被拆分过了 */
  cfguid?: string;
  /** 是否变更 */
  isbg?: string;
  /** 明细订单编号（冻结解冻） */
  mxordbh?: string;
  /** 网超推荐id */
  wcid?: number;
  /** 经贸用的，作废 */
  cfmx?: string;
  /** 经贸用的，作废 */
  ysjf?: string;
  /** 大确认书编号 */
  dqrsbh?: string;
  /** base_product.guid */
  cpguid?: string;
  dwname?: string;
}
