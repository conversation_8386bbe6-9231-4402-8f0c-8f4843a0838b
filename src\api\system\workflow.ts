import request from "@/utils/request";

const WORKFLOW_BASE_URL = "/api/v1/workflow";

const WorkFlowAPI = {
  /** 获取流程日志 */
  getLcrzPage(queryParams?: LcrzQuery) {
    return request<any, PageResult<any[]>>({
      url: `${WORKFLOW_BASE_URL}/lcrz/page`,
      method: "get",
      params: queryParams,
    });
  },

  workflowActive(data: WorkFlowActiveDTO) {
    return request({
      url: `${WORKFLOW_BASE_URL}/active`,
      method: "post",
      data: data,
    });
  },

  getBackNodes(guid: string) {
    return request<any, any[]>({
      url: `${WORKFLOW_BASE_URL}/lcrz/backlist`,
      method: "get",
      params: {
        guid: guid,
      },
    });
  },
  getChart(params: any) {
    return request<any, WorkFlowChartVO[]>({
      url: `${WORKFLOW_BASE_URL}/chart`,
      method: "post",
      params: params,
    });
  },
  //流程日志分页列表
  getPageLcrz(queryParams?: { guid?: string }) {
    return request<any, PageResult<pageLcrzVo[]>>({
      url: `${WORKFLOW_BASE_URL}` + "page-lcrz",
      method: "get",
      params: queryParams,
    });
  },
};

export default WorkFlowAPI;

interface LcrzQuery extends PageQuery {
  guid?: string;
}

export interface WorkFlowActiveDTO {
  /** 唯一标识符 */
  guid?: string;

  /** 当前节点编码 */
  nowCode?: string;

  /** 流程动作 */
  active?: string;

  /** 审核意见 */
  shyj?: string;

  /** 提交用户号 */
  ucode?: string;

  /** 业务名称 */
  sjmc?: string;
}

export interface WorkFlowChartVO {
  /** 操作人编号 */
  username?: string;
  /** 操作人名称 */
  nickname?: string;
  /** 操作时间 */
  time?: string;
  /** 节点编码 */
  netcode?: string;
  /** 节点名称 */
  netname?: string;
  /** 日志表的备注 */
  notes?: string;
  /** 动作 */
  active?: string;
  /** 审核意见 */
  content?: string;
  /** 1,4绿色,2黄色,其他灰色 */
  netstate?: string;
}

export interface pageLcrzVo {
  id?: number;
  guid?: string;
  shyj?: string;
  active?: string;
  czrbh?: string;
  czrname?: string;
  kssj?: string;
  czsj?: string;
  netcode?: string;
  netstate?: string;
  notes?: string;
  netcodename?: string;
  activename?: string;
  czrnamenick?: string;
}
