import request from "@/utils/request";

const ZXCGSQ_BASE_URL = "/api/v1/zxcgSqs";

const ZxcgSqAPI = {
  /** 获取采购申请分页数据 */
  getPage(queryParams?: ZxcgSqPageQuery) {
    return request<any, PageResult<ZxcgSqPageVO[]>>({
      url: `${ZXCGSQ_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },
  /**
   * 获取采购申请表单数据
   *
   * @param id ZxcgSqID
   * @returns ZxcgSq表单数据
   */
  getFormData(id: number) {
    return request<any, ZxcgSqForm>({
      url: `${ZXCGSQ_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /** 添加采购申请*/
  add(data: ZxcgSqForm) {
    return request({
      url: `${ZXCGSQ_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 更新采购申请
   *
   * @param id ZxcgSqID
   * @param data ZxcgSq表单数据
   */
  update(id: number, data: ZxcgSqForm) {
    return request({
      url: `${ZXCGSQ_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 批量删除采购申请，多个以英文逗号(,)分割
   *
   * @param ids 采购申请ID字符串，多个以英文逗号(,)分割
   */
  deleteByIds(ids: string) {
    return request({
      url: `${ZXCGSQ_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
  /**
   * 提交申请
   *
   * @param guid transferID
   * @param data transfer表单数据
   */
  submit(guid: string, data?: any) {
    return request({
      url: `${ZXCGSQ_BASE_URL}/submit/${guid}`,
      method: "post",
      data: data,
    });
  },
};

export default ZxcgSqAPI;
/** 采购申请分页查询参数 */

/** 采购申请分页查询参数 */
export interface ZxcgSqPageQuery extends PageQuery {
  /** 申请名称 */
  sqmc?: string;
  /** 申请人姓名 */
  sqrname?: string;
  /** 申请部门名称 */
  sqbmmc?: string;
}

/** 采购申请表单对象 */
export interface ZxcgSqForm {
  /** 自增ID */
  sqid?: number;
  /** 附件识别码 */
  guid?: string;
  /** 自动生成(BJUT+年月+4位顺序号) */
  sqbh?: string;
  /** 申请名称 */
  sqmc?: string;
  /** 申请人职工号 */
  ucode?: string;
  /** 申请人姓名 */
  sqrname?: string;
  /** 申请时间 */
  sqsj?: Date;
  /** 申请部门编码 */
  sqbm?: string;
  /** 申请部门名称 */
  sqbmmc?: string;
  /** 申请人联系电话(用户表里获取，更新用户表信息) */
  sqrtel?: string;
  /** 申请人座机 */
  sqrzj?: string;
  /** 申请人电子邮箱(用户表里获取，更新用户表信息) */
  sqremail?: string;
  /** 项目名称 */
  xmmc?: string;
  /** 项目编码 */
  xmbm?: string;
  /** 经费类别 */
  jflb?: string;
  /** 预算年度 */
  ysnd?: string;
  /** 主管部门 */
  zgbm?: string;
  /** 部门负责人 */
  bmfzr?: string;
  /** 项目负责人 */
  xmfzr?: string;
  /** 负责人职工号 */
  fzrbm?: string;
  /** 批准项目总金额（万元） */
  xmje?: number;
  /** 所购设备原计划名称 */
  jhmc?: string;
  /** 所购设备原计划预算（万元） */
  jhje?: number;
  /** 采购目录(如工程类型) */
  cgml?: string;
  /** 采购组织形式（如零星采购） */
  cgxs?: string;
  /** 采购方式 */
  cgfs?: string;
  /** 预算金额(元) */
  ysje?: number;
  /** 实际金额 */
  sjje?: number;
  /** 计划交货时间 */
  jhjhrq?: Date;
  /** 送货方式(送货上门、快递送货、自行提货) */
  shfs?: string;
  /** 交货详细地址 */
  address?: string;
  /** 收货人 */
  name?: string;
  /** 收货人职工号 */
  shrbm?: string;
  /** 收货人联系方式 */
  shrtel?: string;
  /** 工作流节点 */
  netcode?: string;
  /** 工作流状态 */
  sjzt?: string;
  /** 下一审批人 */
  nexter?: string;
  /** 下一审批部门|hidden */
  nextbm?: string;
  /** 下节点审批人|hidden */
  nextnodespr?: string;
  /** 采购状态（申请、备案、验收、资产入库、支付、完成） */
  cgzt?: string;
  /** 审批人 */
  spzt?: string;
  /** 资产管理员职工号 */
  zcglr?: string;
  /** 资产管理员姓名 */
  zcname?: string;
  /** 更新标记（当前作用 1：表示独立学院） */
  uflag?: string;
  /** 是否要求单一来源采购 1是,0否 */
  isdy?: string;
  /** 经费性质（财政、非财政） */
  jfxz?: string;
  /** Fgxld */
  fgxld?: string;
  /** Hsbm */
  hsbm?: string;
  /** 产地？ */
  cd?: string;
  /** 采购经费名称 */
  cgjfmc?: string;
  /** ISDyly */
  isdyly?: string;
  /** 是否进口 */
  isjk?: string;
  /** 是否特种设备 */
  istzsb?: string;
  /** 大型仪器 */
  isdxyq?: string;
  /** 科研设备 */
  iskysb?: string;
  /** 购置理由 */
  gzly?: string;
  /** 使用方向 */
  syfx?: string;
  /** ysxm.yslb(2-临时经费) */
  by1?: string;
  /** 是否支持中小企业 */
  by2?: string;
  /** 合同是否跨年 */
  by3?: string;
  /** 是否大仪 */
  by4?: string;
  /** 是否重大项目 */
  by5?: string;
  /** 是否意向公开 */
  by6?: string;
  /** 论证关联ID */
  by7?: string;
  /** 备用 */
  by8?: string;
  /** 不面向中小企业的理由 */
  by9?: string;
  /** 申请类型 */
  sqlx?: string;
  /** 是否与财政审批预算明细相符 */
  ismxxf?: string;
  /** 单一来源论证id */
  dylzid?: string;
  /** 部门分管校领导 */
  bmfgxld?: string;
  /** 意向guid */
  sqyxguid?: string;
  /** 意向名称 */
  yxmc?: string;
  /** 是否政府采购 */
  sfzfcg?: string;
  /** 是否属于按照招标投标法必须进行招标的工程建设项目 */
  sfbxzb?: string;
  cgy?: string;
  /** 是否网超推荐  */
  sfwctj?: string;
  xmfzrbm?: string;
  /** 委托机构code */
  wtjgid?: string;
  /** 委托机构名称 */
  wtjgmc?: string;
  /** 部门审批人 */
  nextspr?: string;
  /** 快速采购大类 */
  kscgfl?: string;
  /** 框架设备的其他供应商或者其他商品 1 */
  kscgother?: string;
  /** 快速采购是否自动下单 1 是 */
  kscgxd?: string;
  /** 履约保证金(元) */
  lybzj?: number;
  /** 是否零星维修工程 */
  by16?: string;
  /** 是否学科办经费1是0否 */
  by17?: string;
  /** 采购受理人 */
  by20?: string;
  /** 通道 0：项目采购 1：零星采购 */
  td?: string;
  /** 业务分管领导 */
  by13?: string;
  /** 30万以下工程派单校区 */
  gzpdxq?: string;
  /** 是否部门自行采购 1是 */
  by14?: string;
  /** 服务类型;服务类型:xcode */
  fwlx?: string;
  /** 拟派施工单位 */
  gcpddw?: string;
  /** 预计当年支付时间 */
  yjdnzfsj?: any;
  /** 预计当年支付金额 */
  yjdnzfje?: number;
  /** 是否发改委项目 */
  sffgw?: string;
  /** 是否属于按照招标投标法必须进行招标的工程建设项目 */
  zbgcxm?: string;
  /** 是否是否信创七类  1是 */
  sfxcfl?: string;
  /** 是否信创  1是 */
  sfxc?: string;
  /** 是否政采云采购 */
  sfzcy?: string;
  /** 是否三重一大，1是 */
  sfszyd?: string;
  /*采购预算编号列表 */
  ysxmbhs?: string;
  /*采购进口论证名称 */
  jklzmc?: string;
  /*采购单一来源论证名称 */
  dylzmc?: string;
  /*采购目录名称 */
  cgmlname?: string;
  /*可行性分析 */
  kxxfx?: string;
  zgbmlist?: string[];
  sqcgfl?: string;
  fjcode?: string;
}

/** 采购申请分页对象 */
export interface ZxcgSqPageVO {
  /** 自增ID */
  sqid?: number;
  /** 附件识别码 */
  guid?: string;
  /** 自动生成(BJUT+年月+4位顺序号) */
  sqbh?: string;
  /** 申请名称 */
  sqmc?: string;
  /** 申请人职工号 */
  ucode?: string;
  /** 申请人姓名 */
  sqrname?: string;
  /** 申请时间 */
  sqsj?: Date;
  /** 申请部门编码 */
  sqbm?: string;
  /** 申请部门名称 */
  sqbmmc?: string;
  /** 申请人联系电话(用户表里获取，更新用户表信息) */
  sqrtel?: string;
  /** 申请人座机 */
  sqrzj?: string;
  /** 申请人电子邮箱(用户表里获取，更新用户表信息) */
  sqremail?: string;
  /** 项目名称 */
  xmmc?: string;
  /** 项目编码 */
  xmbm?: string;
  /** 经费类别 */
  jflb?: string;
  /** 预算年度 */
  ysnd?: string;
  /** 主管部门 */
  zgbm?: string;
  /** 部门负责人 */
  bmfzr?: string;
  /** 项目负责人 */
  xmfzr?: string;
  /** 负责人职工号 */
  fzrbm?: string;
  /** 批准项目总金额（万元） */
  xmje?: number;
  /** 所购设备原计划名称 */
  jhmc?: string;
  /** 所购设备原计划预算（万元） */
  jhje?: number;
  /** 采购目录(如工程类型) */
  cgml?: string;
  /** 采购组织形式（如零星采购） */
  cgxs?: string;
  /** 采购方式 */
  cgfs?: string;
  /** 预算金额(元) */
  ysje?: number;
  /** 实际金额 */
  sjje?: number;
  /** 计划交货时间 */
  jhjhrq?: Date;
  /** 送货方式(送货上门、快递送货、自行提货) */
  shfs?: string;
  /** 交货详细地址 */
  address?: string;
  /** 收货人 */
  shrname?: string;
  /** 收货人职工号 */
  shrbm?: string;
  /** 收货人联系方式 */
  shrtel?: string;
  /** 工作流节点 */
  netcode?: string;
  /** 工作流状态 */
  sjzt?: string;
  /** 下一审批人 */
  nexter?: string;
  /** 下一审批部门|hidden */
  nextbm?: string;
  /** 下节点审批人|hidden */
  nextnodespr?: string;
  /** 采购状态（申请、备案、验收、资产入库、支付、完成） */
  cgzt?: string;
  /** 审批人 */
  spzt?: string;
  /** 资产管理员职工号 */
  zcglr?: string;
  /** 资产管理员姓名 */
  zcname?: string;
  /** 更新标记（当前作用 1：表示独立学院） */
  uflag?: string;
  /** 是否要求单一来源采购 1是,0否 */
  isdy?: string;
  /** 经费性质（财政、非财政） */
  jfxz?: string;
  /** Fgxld */
  fgxld?: string;
  /** Hsbm */
  hsbm?: string;
  /** 产地？ */
  cd?: string;
  /** 采购经费名称 */
  cgjfmc?: string;
  /** ISDyly */
  isdyly?: string;
  /** 是否进口 */
  isjk?: string;
  /** 是否特种设备 */
  istzsb?: string;
  /** 大型仪器 */
  isdxyq?: string;
  /** 科研设备 */
  iskysb?: string;
  /** 购置理由 */
  gzly?: string;
  /** 使用方向 */
  syfx?: string;
  /** ysxm.yslb(2-临时经费) */
  by1?: string;
  /** 是否支持中小企业 */
  by2?: string;
  /** 合同是否跨年 */
  by3?: string;
  /** 是否大仪 */
  by4?: string;
  /** 是否重大项目 */
  by5?: string;
  /** 是否意向公开 */
  by6?: string;
  /** 论证关联ID */
  by7?: string;
  /** 备用 */
  by8?: string;
  /** 不面向中小企业的理由 */
  by9?: string;
  /** 申请类型 */
  sqlx?: string;
  /** 是否与财政审批预算明细相符 */
  ismxxf?: string;
  /** 单一来源论证id */
  dylzid?: string;
  /** 部门分管校领导 */
  bmfgxld?: string;
  /** 意向guid */
  sqyxguid?: string;
  /** 意向名称 */
  yxmc?: string;
  /** 是否政府采购 */
  sfzfcg?: string;
  /** 是否属于按照招标投标法必须进行招标的工程建设项目 */
  sfbxzb?: string;
  cgy?: string;
  /** 是否网超推荐  */
  sfwctj?: string;
  xmfzrbm?: string;
  /** 委托机构code */
  wtjgid?: string;
  /** 委托机构名称 */
  wtjgmc?: string;
  /** 部门审批人 */
  nextspr?: string;
  /** 快速采购大类 */
  kscgfl?: string;
  /** 框架设备的其他供应商或者其他商品 1 */
  kscgother?: string;
  /** 快速采购是否自动下单 1 是 */
  kscgxd?: string;
  /** 履约保证金(元) */
  lybzj?: number;
  /** 是否零星维修工程 */
  by16?: string;
  /** 是否学科办经费1是0否 */
  by17?: string;
  /** 采购受理人 */
  by20?: string;
  /** 通道 0：项目采购 1：零星采购 */
  td?: string;
  /** 业务分管领导 */
  by13?: string;
  /** 30万以下工程派单校区 */
  gzpdxq?: string;
  /** 是否部门自行采购 1是 */
  by14?: string;
  /** 服务类型;服务类型:xcode */
  fwlx?: string;
  /** 拟派施工单位 */
  gcpddw?: string;
  /** 预计当年支付时间 */
  yjdnzfsj?: Date;
  /** 预计当年支付金额 */
  yjdnzfje?: number;
  /** 是否发改委项目 */
  sffgw?: string;
  /** 是否属于按照招标投标法必须进行招标的工程建设项目 */
  zbgcxm?: string;
  /** 是否是否信创七类  1是 */
  sfxcfl?: string;
  /** 是否信创  1是 */
  sfxc?: string;
  /** 是否政采云采购 */
  sfzcy?: string;
  /** 是否三重一大，1是 */
  sfszyd?: string;
}
